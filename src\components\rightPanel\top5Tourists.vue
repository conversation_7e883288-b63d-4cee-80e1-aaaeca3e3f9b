<!-- 桥隧健康度 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>桥隧健康度</span>
        <span class="header-subtitle">BRIDGE AND TUNNEL HEALTH</span>
      </div>
    </template>

    <template #content>
      <CEcharts ref="chartRef" :option="option" />
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})
const chartRef = ref()

// 颜色配置
const color = [
    '#00FF7F',  // I基本完好 - 绿色
    '#4169E1',  // II轻微异常 - 蓝色
    '#1E90FF',  // III中等异常 - 浅蓝色
    '#00BFFF',  // IV严重异常 - 天蓝色
]

// 图例数据
const legend = [
    'I基本完好',
    'II轻微异常',
    'III中等异常',
    'IV严重异常'
]

// 系列数据
const seriesData = [
    { "name": "I基本完好", "value": 31 },
    { "name": "II轻微异常", "value": 28 },
    { "name": "III中等异常", "value": 18 },
    { "name": "IV严重异常", "value": 15 }
]

// 默认选中功能
const getDefaultSelected = (myChart) => {
    let index = 0;
    myChart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: 0,
    });
    myChart.on('mouseover', (e) => {
      if (e.dataIndex !== index) {
        myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: index,
        });
      }
    });
    myChart.on('mouseout', (e) => {
      index = e.dataIndex;
      myChart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: e.dataIndex,
      });
    });
}

// 创建图表配置
const createChart = () => {
  return {
    backgroundColor: 'transparent',
    color: color,
    grid: {
        top: '15%',
        left: 0,
        right: '1%',
        bottom: 5,
        containLabel: true,
    },
    legend: {
        orient: 'vertical',
        top: 'center',
        right: '5%',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 15,
        textStyle: {
            color: '#fff',
            fontSize: 14,
            align: 'left',
            verticalAlign: 'middle',
        },
        data: legend,
        formatter: (name) => {
            if (seriesData.length) {
                const item = seriesData.filter((item) => item.name === name)[0];
                return `${name}    ${item.value}`;
            }
        },
    },
    graphic: [
      {
        type: 'text',
        left: '25%',
        top: 'center',
        style: {
          text: '16.3%\nIV严重异常',
          textAlign: 'center',
          fill: '#fff',
          fontSize: 16,
          fontWeight: 'bold'
        }
      }
    ],
    series: [{
        name: '桥隧健康度',
        type: 'pie',
        center: ['25%', '50%'],
        radius: ['40%', '65%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        data: seriesData,
    }],
  }
}

onMounted(() => {
  option.value = createChart()

  // 等待图表渲染完成后设置默认选中
  nextTick(() => {
    setTimeout(() => {
      if (chartRef.value && chartRef.value.chart) {
        getDefaultSelected(chartRef.value.chart)
      }
    }, 100)
  })
})
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}
</style>
