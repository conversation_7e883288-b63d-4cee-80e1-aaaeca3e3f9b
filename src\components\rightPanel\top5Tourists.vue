<!-- 桥隧健康度 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>桥隧健康度</span>
        <span class="header-subtitle">BRIDGE AND TUNNEL HEALTH</span>
      </div>
    </template>

    <template #content>
      <CEcharts ref="chartRef" :option="option" />
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})
const chartRef = ref()

// 颜色配置
const color = [
    '#0CD2E6',
    '#3751E6',
    '#FFC722',
    '#886EFF',
    '#008DEC',
    '#114C90',
    '#00BFA5',
]

// 图例数据
const legend = [
    'A需求类型',
    'B需求类型',
    'C需求类型',
    'D需求类型',
    'E需求类型',
    '其他'
]

// 系列数据
const seriesData = [
    { "name": "A需求类型", "value": 30 },
    { "name": "B需求类型", "value": 10 },
    { "name": "C需求类型", "value": 15 },
    { "name": "D需求类型", "value": 23 },
    { "name": "E需求类型", "value": 10 },
    { "name": "其他", "value": 12 }
]

// 默认选中功能
const getDefaultSelected = (myChart) => {
    let index = 0;
    myChart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: 0,
    });
    myChart.on('mouseover', (e) => {
      if (e.dataIndex !== index) {
        myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: index,
        });
      }
    });
    myChart.on('mouseout', (e) => {
      index = e.dataIndex;
      myChart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: e.dataIndex,
      });
    });
}

// 创建图表配置
const createChart = () => {
  return {
    backgroundColor: '#050e31',
    color: color,
    title: {
        top: 20,
        text: '自定义legend、默认选中',
        textStyle: {
            fontSize: 20,
            color: '#DDEEFF',
        },
    },
    grid: {
        top: '15%',
        left: 0,
        right: '1%',
        bottom: 5,
        containLabel: true,
    },
    legend: {
        orient: 'vertical',
        top: 'center',
        right: 50,
        textStyle: {
            align: 'left',
            verticalAlign: 'middle',
            rich: {
                name: {
                    color: 'rgba(255,255,255,0.5)',
                    fontSize: 10,
                },
                value: {
                    color: 'rgba(255,255,255,0.5)',
                    fontSize: 10,
                },
                rate: {
                    color: 'rgba(255,255,255,0.9)',
                    fontSize: 10,
                },
            },
        },
        data: legend,
        formatter: (name) => {
            if (seriesData.length) {
                const item = seriesData.filter((item) => item.name === name)[0];
                return `{name|${name}：}{value| ${item.value}} {rate| ${item.value}%}`;
            }
        },
    },
    series: [{
        name: '需求类型占比',
        type: 'pie',
        center: ['50%', '50%'],
        radius: ['45%', '65%'],
        label: {
            normal: {
                show: false,
                position: 'center',
                formatter: '{value|{c}}\n{label|{b}}',
                rich: {
                    value: {
                        padding: 5,
                        align: 'center',
                        verticalAlign: 'middle',
                        fontSize: 32,
                    },
                    label: {
                        align: 'center',
                        verticalAlign: 'middle',
                        fontSize: 16,
                    },
                },
            },
            emphasis: {
                show: true,
                textStyle: {
                    fontSize: '12',
                },
            },
        },
        labelLine: {
            show: false,
            length: 0,
            length2: 0,
        },
        data: seriesData,
    }],
  }
}

onMounted(() => {
  option.value = createChart()

  // 等待图表渲染完成后设置默认选中
  nextTick(() => {
    setTimeout(() => {
      if (chartRef.value && chartRef.value.chart) {
        getDefaultSelected(chartRef.value.chart)
      }
    }, 100)
  })
})
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}
</style>
